package main

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// OpenAIClient handles communication with OpenAI API
type OpenAIClient struct {
	apiKey  string
	baseURL string
	client  *http.Client
}

// NewOpenAIClient creates a new OpenAI client
func NewOpenAIClient(apiKey, baseURL string, timeout time.Duration) *OpenAIClient {
	return &OpenAIClient{
		apiKey:  apiKey,
		baseURL: baseURL,
		client: &http.Client{
			Timeout: timeout,
		},
	}
}

// ImageGenerationRequest represents the request payload for image generation
type ImageGenerationRequest struct {
	Model          string `json:"model"`
	Prompt         string `json:"prompt"`
	N              int    `json:"n"`
	Size           string `json:"size"`
	Quality        string `json:"quality,omitempty"`
	ResponseFormat string `json:"response_format,omitempty"`
	Stream         bool   `json:"stream,omitempty"`
}

// ImageGenerationResponse represents the response from OpenAI image generation API
type ImageGenerationResponse struct {
	Created int64 `json:"created"`
	Data    []struct {
		URL           string `json:"url,omitempty"`
		B64JSON       string `json:"b64_json,omitempty"`
		RevisedPrompt string `json:"revised_prompt,omitempty"`
	} `json:"data"`
}

// StreamingResult contains the results and timing information from a streaming request
type StreamingResult struct {
	Response          *ImageGenerationResponse
	FirstResponseTime time.Duration
	CompletionTime    time.Duration
	ProgressEvents    []ProgressEvent
	Error             error
}

// ProgressEvent represents a progress update during streaming
type ProgressEvent struct {
	Timestamp time.Time
	Progress  int
	Status    string
}

// StreamEvent represents a streaming event from OpenAI API
type StreamEvent struct {
	Event string          `json:"event"`
	Data  json.RawMessage `json:"data"`
}

// ErrorResponse represents an error response from OpenAI API
type ErrorResponse struct {
	Error struct {
		Message string `json:"message"`
		Type    string `json:"type"`
		Code    string `json:"code"`
	} `json:"error"`
}

// GenerateImage generates an image using OpenAI's gpt-image-1 API with streaming support
func (c *OpenAIClient) GenerateImage(req ImageGenerationRequest) (*StreamingResult, error) {
	startTime := time.Now()

	// Enable streaming for gpt-image-1
	req.Stream = true

	// Prepare request body
	reqBody, err := json.Marshal(req)
	if err != nil {
		return &StreamingResult{Error: fmt.Errorf("failed to marshal request: %w", err)}, err
	}

	// Create HTTP request
	httpReq, err := http.NewRequest("POST", c.baseURL+"/v1/images/generations", bytes.NewBuffer(reqBody))
	if err != nil {
		return &StreamingResult{Error: fmt.Errorf("failed to create request: %w", err)}, err
	}

	// Set headers for streaming
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+c.apiKey)
	httpReq.Header.Set("Accept", "text/event-stream")

	// Make request
	resp, err := c.client.Do(httpReq)
	if err != nil {
		return &StreamingResult{
			Error:          fmt.Errorf("request failed: %w", err),
			CompletionTime: time.Since(startTime),
		}, err
	}
	defer resp.Body.Close()

	// Check for error response
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		var errorResp ErrorResponse
		if err := json.Unmarshal(body, &errorResp); err != nil {
			return &StreamingResult{
				Error:          fmt.Errorf("API error (status %d): %s", resp.StatusCode, string(body)),
				CompletionTime: time.Since(startTime),
			}, fmt.Errorf("API error (status %d): %s", resp.StatusCode, string(body))
		}
		return &StreamingResult{
			Error:          fmt.Errorf("API error: %s", errorResp.Error.Message),
			CompletionTime: time.Since(startTime),
		}, fmt.Errorf("API error: %s", errorResp.Error.Message)
	}

	// Process streaming response
	return c.processStreamingResponse(resp.Body, startTime)
}

// processStreamingResponse processes the streaming response from OpenAI gpt-image-1 API
func (c *OpenAIClient) processStreamingResponse(body io.Reader, startTime time.Time) (*StreamingResult, error) {
	result := &StreamingResult{
		ProgressEvents: make([]ProgressEvent, 0),
	}

	scanner := bufio.NewScanner(body)
	firstResponse := true

	for scanner.Scan() {
		line := scanner.Text()

		// Skip empty lines
		if line == "" {
			continue
		}

		// Parse Server-Sent Events format
		if strings.HasPrefix(line, "data: ") {
			data := strings.TrimPrefix(line, "data: ")

			// Skip [DONE] marker
			if data == "[DONE]" {
				break
			}

			// Record first response time
			if firstResponse {
				result.FirstResponseTime = time.Since(startTime)
				firstResponse = false
			}

			// Parse JSON data
			var event map[string]interface{}
			if err := json.Unmarshal([]byte(data), &event); err != nil {
				continue // Skip malformed JSON
			}

			// Handle different event types for gpt-image-1
			if eventType, ok := event["type"].(string); ok {
				switch eventType {
				case "progress":
					// Handle progress events
					if progressData, ok := event["data"].(map[string]interface{}); ok {
						progress := 0
						status := ""

						if p, ok := progressData["progress"].(float64); ok {
							progress = int(p)
						}
						if s, ok := progressData["status"].(string); ok {
							status = s
						}

						result.ProgressEvents = append(result.ProgressEvents, ProgressEvent{
							Timestamp: time.Now(),
							Progress:  progress,
							Status:    status,
						})
					}
				case "image.generated":
					// Final result with image URL
					if resultData, ok := event["data"].(map[string]interface{}); ok {
						var imageResp ImageGenerationResponse
						resultBytes, _ := json.Marshal(resultData)
						if err := json.Unmarshal(resultBytes, &imageResp); err == nil {
							result.Response = &imageResp
						}
					}
				case "image.url":
					// Handle direct URL response
					if urlData, ok := event["data"].(map[string]interface{}); ok {
						if url, ok := urlData["url"].(string); ok {
							result.Response = &ImageGenerationResponse{
								Created: time.Now().Unix(),
								Data: []struct {
									URL           string `json:"url,omitempty"`
									B64JSON       string `json:"b64_json,omitempty"`
									RevisedPrompt string `json:"revised_prompt,omitempty"`
								}{
									{URL: url},
								},
							}
						}
					}
				}
			} else {
				// Handle direct response format (fallback)
				var imageResp ImageGenerationResponse
				if err := json.Unmarshal([]byte(data), &imageResp); err == nil {
					result.Response = &imageResp
					if firstResponse {
						result.FirstResponseTime = time.Since(startTime)
						firstResponse = false
					}
				}
			}
		}
	}

	result.CompletionTime = time.Since(startTime)

	if err := scanner.Err(); err != nil {
		result.Error = fmt.Errorf("error reading stream: %w", err)
		return result, err
	}

	if result.Response == nil {
		result.Error = fmt.Errorf("no valid response received")
		return result, result.Error
	}

	return result, nil
}
