package main

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// OpenAIClient handles communication with OpenAI API
type OpenAIClient struct {
	apiKey  string
	baseURL string
	client  *http.Client
}

// NewOpenAIClient creates a new OpenAI client
func NewOpenAIClient(apiKey, baseURL string, timeout time.Duration) *OpenAIClient {
	return &OpenAIClient{
		apiKey:  apiKey,
		baseURL: baseURL,
		client: &http.Client{
			Timeout: timeout,
		},
	}
}

// ChatCompletionRequest represents the request payload for chat completion with image generation
type ChatCompletionRequest struct {
	Model    string    `json:"model"`
	Messages []Message `json:"messages"`
	Stream   bool      `json:"stream"`
}

// Message represents a chat message
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// ImageGenerationRequest represents the legacy request payload (kept for compatibility)
type ImageGenerationRequest struct {
	Model          string `json:"model"`
	Prompt         string `json:"prompt"`
	N              int    `json:"n"`
	Size           string `json:"size"`
	Quality        string `json:"quality,omitempty"`
	ResponseFormat string `json:"response_format,omitempty"`
}

// ChatCompletionResponse represents the response from chat completion API
type ChatCompletionResponse struct {
	ID      string   `json:"id"`
	Object  string   `json:"object"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
}

// Choice represents a choice in chat completion response
type Choice struct {
	Index        int     `json:"index"`
	Delta        *Delta  `json:"delta,omitempty"`
	Message      *Delta  `json:"message,omitempty"`
	FinishReason *string `json:"finish_reason,omitempty"`
}

// Delta represents the delta content in streaming response
type Delta struct {
	Role    string `json:"role,omitempty"`
	Content string `json:"content,omitempty"`
}

// ImageGenerationResponse represents the response from OpenAI image generation API
type ImageGenerationResponse struct {
	Created int64 `json:"created"`
	Data    []struct {
		URL           string `json:"url,omitempty"`
		B64JSON       string `json:"b64_json,omitempty"`
		RevisedPrompt string `json:"revised_prompt,omitempty"`
	} `json:"data"`
}

// StreamingResult contains the results and timing information from a streaming request
type StreamingResult struct {
	Response          *ImageGenerationResponse
	ChatContent       string
	FirstResponseTime time.Duration
	CompletionTime    time.Duration
	ProgressEvents    []ProgressEvent
	Error             error
}

// ProgressEvent represents a progress update during streaming
type ProgressEvent struct {
	Timestamp time.Time
	Progress  int
	Status    string
}

// StreamEvent represents a streaming event from OpenAI API
type StreamEvent struct {
	Event string          `json:"event"`
	Data  json.RawMessage `json:"data"`
}

// ErrorResponse represents an error response from OpenAI API
type ErrorResponse struct {
	Error struct {
		Message string `json:"message"`
		Type    string `json:"type"`
		Code    string `json:"code"`
	} `json:"error"`
}

// GenerateImage generates an image using OpenAI's gpt-image-1 API via chat completion with streaming
func (c *OpenAIClient) GenerateImage(req ImageGenerationRequest) (*StreamingResult, error) {
	startTime := time.Now()

	// Create chat completion request with image generation prompt
	chatReq := ChatCompletionRequest{
		Model:  req.Model,
		Stream: true,
		Messages: []Message{
			{
				Role: "user",
				Content: fmt.Sprintf(`Please generate an image with the following specifications:
- Prompt: %s
- Size: %s
- Quality: %s
- Number of images: %d

Please respond with a JSON object containing the image generation request and then generate the image.`,
					req.Prompt, req.Size, req.Quality, req.N),
			},
		},
	}

	// Prepare request body
	reqBody, err := json.Marshal(chatReq)
	if err != nil {
		return &StreamingResult{Error: fmt.Errorf("failed to marshal request: %w", err)}, err
	}

	// Create HTTP request for chat completions
	httpReq, err := http.NewRequest("POST", c.baseURL+"/chat/completions", bytes.NewBuffer(reqBody))
	if err != nil {
		return &StreamingResult{Error: fmt.Errorf("failed to create request: %w", err)}, err
	}

	// Set headers for streaming
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+c.apiKey)
	httpReq.Header.Set("Accept", "text/event-stream")

	// Make request
	resp, err := c.client.Do(httpReq)
	if err != nil {
		return &StreamingResult{
			Error:          fmt.Errorf("request failed: %w", err),
			CompletionTime: time.Since(startTime),
		}, err
	}
	defer resp.Body.Close()

	// Check for error response
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		var errorResp ErrorResponse
		if err := json.Unmarshal(body, &errorResp); err != nil {
			return &StreamingResult{
				Error:          fmt.Errorf("API error (status %d): %s", resp.StatusCode, string(body)),
				CompletionTime: time.Since(startTime),
			}, fmt.Errorf("API error (status %d): %s", resp.StatusCode, string(body))
		}
		return &StreamingResult{
			Error:          fmt.Errorf("API error: %s", errorResp.Error.Message),
			CompletionTime: time.Since(startTime),
		}, fmt.Errorf("API error: %s", errorResp.Error.Message)
	}

	// Process streaming chat completion response
	return c.processChatStreamingResponse(resp.Body, startTime)
}

// processChatStreamingResponse processes the streaming response from OpenAI chat completion API
func (c *OpenAIClient) processChatStreamingResponse(body io.Reader, startTime time.Time) (*StreamingResult, error) {
	result := &StreamingResult{
		ProgressEvents: make([]ProgressEvent, 0),
		ChatContent:    "",
	}

	scanner := bufio.NewScanner(body)
	firstResponse := true

	for scanner.Scan() {
		line := scanner.Text()

		// Skip empty lines
		if line == "" {
			continue
		}

		// Parse Server-Sent Events format
		if strings.HasPrefix(line, "data: ") {
			data := strings.TrimPrefix(line, "data: ")

			// Skip [DONE] marker
			if data == "[DONE]" {
				break
			}

			// Record first response time
			if firstResponse {
				result.FirstResponseTime = time.Since(startTime)
				firstResponse = false
			}

			// Parse JSON data
			var chatResp ChatCompletionResponse
			if err := json.Unmarshal([]byte(data), &chatResp); err != nil {
				continue // Skip malformed JSON
			}

			// Process chat completion response
			if len(chatResp.Choices) > 0 {
				choice := chatResp.Choices[0]

				// Accumulate content from delta
				if choice.Delta != nil && choice.Delta.Content != "" {
					result.ChatContent += choice.Delta.Content
				}

				// Check if we have a finish reason
				if choice.FinishReason != nil && *choice.FinishReason == "stop" {
					// Try to extract image URL from the accumulated content
					if imageURL := c.extractImageURLFromContent(result.ChatContent); imageURL != "" {
						result.Response = &ImageGenerationResponse{
							Created: time.Now().Unix(),
							Data: []struct {
								URL           string `json:"url,omitempty"`
								B64JSON       string `json:"b64_json,omitempty"`
								RevisedPrompt string `json:"revised_prompt,omitempty"`
							}{
								{URL: imageURL},
							},
						}
					}
				}
			}
		}
	}

	result.CompletionTime = time.Since(startTime)

	if err := scanner.Err(); err != nil {
		result.Error = fmt.Errorf("error reading stream: %w", err)
		return result, err
	}

	// If we didn't get a response from the finish reason, try to extract from final content
	if result.Response == nil && result.ChatContent != "" {
		if imageURL := c.extractImageURLFromContent(result.ChatContent); imageURL != "" {
			result.Response = &ImageGenerationResponse{
				Created: time.Now().Unix(),
				Data: []struct {
					URL           string `json:"url,omitempty"`
					B64JSON       string `json:"b64_json,omitempty"`
					RevisedPrompt string `json:"revised_prompt,omitempty"`
				}{
					{URL: imageURL},
				},
			}
		}
	}

	if result.Response == nil {
		result.Error = fmt.Errorf("no valid image URL found in response: %s", result.ChatContent)
		return result, result.Error
	}

	return result, nil
}

// extractImageURLFromContent extracts image URL from chat content
func (c *OpenAIClient) extractImageURLFromContent(content string) string {
	// Try to parse as JSON first (if the model returns a JSON response)
	var jsonResp map[string]interface{}
	if err := json.Unmarshal([]byte(content), &jsonResp); err == nil {
		// Look for image URL in various possible JSON structures
		if data, ok := jsonResp["data"].([]interface{}); ok && len(data) > 0 {
			if item, ok := data[0].(map[string]interface{}); ok {
				if url, ok := item["url"].(string); ok {
					return url
				}
			}
		}
		if url, ok := jsonResp["url"].(string); ok {
			return url
		}
		if imageURL, ok := jsonResp["image_url"].(string); ok {
			return imageURL
		}
	}

	// Try to extract URL using simple string matching
	if strings.Contains(content, "http") {
		// Simple extraction for URLs
		lines := strings.Split(content, "\n")
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if strings.HasPrefix(line, "http") && (strings.Contains(line, ".png") ||
				strings.Contains(line, ".jpg") || strings.Contains(line, ".jpeg") ||
				strings.Contains(line, "dalle") || strings.Contains(line, "blob.core")) {
				// Clean up the URL
				url := strings.Trim(line, `"',()[]{}`)
				if strings.HasPrefix(url, "http") {
					return url
				}
			}
		}

		// Also try to find URLs within quotes
		words := strings.Fields(content)
		for _, word := range words {
			word = strings.Trim(word, `"',()[]{}`)
			if strings.HasPrefix(word, "http") && (strings.Contains(word, ".png") ||
				strings.Contains(word, ".jpg") || strings.Contains(word, ".jpeg") ||
				strings.Contains(word, "dalle") || strings.Contains(word, "blob.core")) {
				return word
			}
		}
	}

	return ""
}
