package main

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// OpenAIClient handles communication with OpenAI API
type OpenAIClient struct {
	apiKey  string
	baseURL string
	client  *http.Client
}

// NewOpenAIClient creates a new OpenAI client
func NewOpenAIClient(apiKey, baseURL string, timeout time.Duration) *OpenAIClient {
	return &OpenAIClient{
		apiKey:  apiKey,
		baseURL: baseURL,
		client: &http.Client{
			Timeout: timeout,
		},
	}
}

// ImageGenerationRequest represents the request payload for image generation
type ImageGenerationRequest struct {
	Model          string `json:"model"`
	Prompt         string `json:"prompt"`
	N              int    `json:"n"`
	Size           string `json:"size"`
	Quality        string `json:"quality,omitempty"`
	ResponseFormat string `json:"response_format,omitempty"`
}

// ImageGenerationResponse represents the response from OpenAI image generation API
type ImageGenerationResponse struct {
	Created int64 `json:"created"`
	Data    []struct {
		URL           string `json:"url,omitempty"`
		B64JSON       string `json:"b64_json,omitempty"`
		RevisedPrompt string `json:"revised_prompt,omitempty"`
	} `json:"data"`
}

// ErrorResponse represents an error response from OpenAI API
type ErrorResponse struct {
	Error struct {
		Message string `json:"message"`
		Type    string `json:"type"`
		Code    string `json:"code"`
	} `json:"error"`
}

// GenerateImage generates an image using OpenAI's DALL-E API
func (c *OpenAIClient) GenerateImage(req ImageGenerationRequest) (*ImageGenerationResponse, time.Duration, error) {
	startTime := time.Now()

	// Prepare request body
	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	httpReq, err := http.NewRequest("POST", c.baseURL+"/v1/images/generations", bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, 0, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+c.apiKey)

	// Make request
	resp, err := c.client.Do(httpReq)
	if err != nil {
		return nil, time.Since(startTime), fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, time.Since(startTime), fmt.Errorf("failed to read response: %w", err)
	}

	duration := time.Since(startTime)

	// Check for error response
	if resp.StatusCode != http.StatusOK {
		var errorResp ErrorResponse
		if err := json.Unmarshal(body, &errorResp); err != nil {
			return nil, duration, fmt.Errorf("API error (status %d): %s", resp.StatusCode, string(body))
		}
		return nil, duration, fmt.Errorf("API error: %s", errorResp.Error.Message)
	}

	// Parse successful response
	var imageResp ImageGenerationResponse
	if err := json.Unmarshal(body, &imageResp); err != nil {
		return nil, duration, fmt.Errorf("failed to parse response: %w", err)
	}

	return &imageResp, duration, nil
}
