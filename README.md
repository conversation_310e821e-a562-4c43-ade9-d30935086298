# OpenAI Image Generation Load Test

这是一个针对OpenAI gpt-image-1图像生成模型的压力测试工具，支持流式响应，可以测试API的性能、稳定性和响应时间。

## 功能特性

- 🚀 **并发压测**: 支持配置并发请求数量
- 🌊 **流式响应**: 支持gpt-image-1模型的流式调用，实时监控生成进度
- 📊 **详细统计**: 记录首字返回时间、完成时间、成功率等指标
- 🎨 **智能提示词**: 自动生成多样化的图像生成提示词
- 💾 **结果保存**: 自动下载生成的图片并保存测试结果
- 📈 **实时监控**: 实时显示测试进度和统计信息
- ⚙️ **灵活配置**: 通过YAML文件配置所有参数

## 快速开始

### 1. 配置API密钥

编辑 `config.yaml` 文件，设置您的OpenAI API密钥：

```yaml
openai:
  api_key: "your-actual-openai-api-key-here"
  base_url: "https://api.openai.com/v1"
  model: "gpt-image-1"
```

### 2. 安装依赖

```bash
go mod tidy
```

### 3. 运行压测

```bash
go run .
```

或者指定配置文件：

```bash
go run . -config=custom_config.yaml
```

### 4. 编译运行

```bash
go build -o load-test
./load-test
```

## 配置说明

### 基本配置

```yaml
# OpenAI API配置
openai:
  api_key: "your-openai-api-key-here"  # 必填：OpenAI API密钥
  base_url: "https://api.openai.com/v1"  # API基础URL
  model: "dall-e-3"  # 使用的模型

# 压测配置
load_test:
  concurrent_requests: 50  # 并发请求数
  total_requests: 100      # 总请求数
  timeout_seconds: 300     # 请求超时时间（秒）

# 图像参数
image_params:
  size: "1024x1024"  # 图像尺寸
  quality: "standard"  # 图像质量
  n: 1  # 每次请求生成的图像数量

# 输出配置
output:
  save_images: true  # 是否保存生成的图像
  images_dir: "./generated_images"  # 图像保存目录
  results_file: "./load_test_results.json"  # 结果文件路径
```

## 输出结果

### 控制台输出

程序会实时显示：
- 测试配置信息
- 实时进度更新
- 当前统计信息
- 最终汇总报告

### 结果文件

测试完成后会生成JSON格式的详细结果文件，包含：

```json
{
  "summary": {
    "total_requests": 100,
    "successful_requests": 95,
    "failed_requests": 5,
    "success_rate": 95.0,
    "avg_completion_time": "45.2s",
    "avg_first_response_time": "2.1s",
    "requests_per_second": 1.2
  },
  "results": [
    {
      "id": 1,
      "prompt": "a majestic lion, oil painting, during golden hour",
      "start_time": "2024-01-01T10:00:00Z",
      "first_response_time": "2024-01-01T10:00:02Z",
      "end_time": "2024-01-01T10:00:45Z",
      "duration": "45.2s",
      "first_response_duration": "2.1s",
      "success": true,
      "image_url": "https://...",
      "image_path": "./generated_images/image_1_1704096000.png"
    }
  ]
}
```

### 生成的图像

如果启用了 `save_images`，所有成功生成的图像都会保存到指定目录中。

## 流式响应说明

本工具专门针对OpenAI的gpt-image-1模型进行了优化，通过Chat Completion API的流式响应来进行图像生成：

- **Chat Completion流式调用**: 使用`/chat/completions`端点进行流式图像生成
- **JSON透传方式**: 通过聊天消息传递图像生成参数，模型返回图像URL
- **实时内容流**: 通过Server-Sent Events (SSE) 接收实时的聊天响应流
- **首字返回时间**: 记录收到第一个流式响应的时间
- **内容解析**: 从聊天响应中智能提取图像URL

### 工作原理

1. **请求转换**: 将图像生成参数转换为聊天消息
2. **流式调用**: 使用chat completion的stream模式
3. **内容累积**: 实时累积流式响应的内容
4. **URL提取**: 从完整响应中提取图像URL
5. **结果返回**: 构造标准的图像生成响应格式

## 统计指标说明

- **首字返回时间**: 从发送请求到收到第一个chat completion流式响应的时间
- **完成时间**: 整个请求的总耗时（包括聊天响应、图像生成和下载）
- **成功率**: 成功提取到图像URL的请求占总请求的百分比
- **聊天内容**: 记录完整的chat completion响应内容
- **P50/P95/P99**: 分别表示50%、95%、99%的请求在该时间内完成

## 注意事项

1. **API配额**: 请确保您的OpenAI账户有足够的API配额
2. **费用控制**: gpt-image-1 API按请求收费，请注意控制测试规模
3. **网络环境**: 确保网络连接稳定，避免因网络问题影响测试结果
4. **并发限制**: OpenAI API有并发限制，建议从较小的并发数开始测试

## 故障排除

### 常见错误

1. **API密钥错误**: 检查config.yaml中的api_key是否正确
2. **网络超时**: 增加timeout_seconds的值
3. **并发过高**: 降低concurrent_requests的值
4. **磁盘空间不足**: 检查images_dir目录的可用空间

### 调试模式

如需更详细的日志信息，可以修改代码添加调试输出。

## 许可证

MIT License
