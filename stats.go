package main

import (
	"encoding/json"
	"fmt"
	"os"
	"sort"
	"strings"
	"sync"
	"time"
)

// RequestResult represents the result of a single image generation request
type RequestResult struct {
	ID               int           `json:"id"`
	Prompt           string        `json:"prompt"`
	StartTime        time.Time     `json:"start_time"`
	FirstResponseTime *time.Time   `json:"first_response_time,omitempty"`
	EndTime          *time.Time    `json:"end_time,omitempty"`
	Duration         time.Duration `json:"duration"`
	FirstResponseDuration *time.Duration `json:"first_response_duration,omitempty"`
	Success          bool          `json:"success"`
	Error            string        `json:"error,omitempty"`
	ImageURL         string        `json:"image_url,omitempty"`
	ImagePath        string        `json:"image_path,omitempty"`
	ChatContent      string        `json:"chat_content,omitempty"`
}

// Stats collects and manages load test statistics
type Stats struct {
	mu      sync.RWMutex
	results []RequestResult
	startTime time.Time
}

// NewStats creates a new stats collector
func NewStats() *Stats {
	return &Stats{
		results:   make([]RequestResult, 0),
		startTime: time.Now(),
	}
}

// AddResult adds a request result to the statistics
func (s *Stats) AddResult(result RequestResult) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.results = append(s.results, result)
}

// GetResults returns a copy of all results
func (s *Stats) GetResults() []RequestResult {
	s.mu.RLock()
	defer s.mu.RUnlock()
	results := make([]RequestResult, len(s.results))
	copy(results, s.results)
	return results
}

// GetSummary calculates and returns summary statistics
func (s *Stats) GetSummary() Summary {
	s.mu.RLock()
	defer s.mu.RUnlock()

	summary := Summary{
		TotalRequests: len(s.results),
		StartTime:     s.startTime,
		EndTime:       time.Now(),
	}

	if len(s.results) == 0 {
		return summary
	}

	var successCount int
	var durations []time.Duration
	var firstResponseDurations []time.Duration

	for _, result := range s.results {
		if result.Success {
			successCount++
			durations = append(durations, result.Duration)
			if result.FirstResponseDuration != nil {
				firstResponseDurations = append(firstResponseDurations, *result.FirstResponseDuration)
			}
		}
	}

	summary.SuccessfulRequests = successCount
	summary.FailedRequests = len(s.results) - successCount
	summary.SuccessRate = float64(successCount) / float64(len(s.results)) * 100

	if len(durations) > 0 {
		sort.Slice(durations, func(i, j int) bool {
			return durations[i] < durations[j]
		})

		summary.AvgCompletionTime = calculateAverage(durations)
		summary.MinCompletionTime = durations[0]
		summary.MaxCompletionTime = durations[len(durations)-1]
		summary.P50CompletionTime = durations[len(durations)/2]
		summary.P95CompletionTime = durations[int(float64(len(durations))*0.95)]
		summary.P99CompletionTime = durations[int(float64(len(durations))*0.99)]
	}

	if len(firstResponseDurations) > 0 {
		sort.Slice(firstResponseDurations, func(i, j int) bool {
			return firstResponseDurations[i] < firstResponseDurations[j]
		})

		summary.AvgFirstResponseTime = calculateAverage(firstResponseDurations)
		summary.MinFirstResponseTime = firstResponseDurations[0]
		summary.MaxFirstResponseTime = firstResponseDurations[len(firstResponseDurations)-1]
		summary.P50FirstResponseTime = firstResponseDurations[len(firstResponseDurations)/2]
		summary.P95FirstResponseTime = firstResponseDurations[int(float64(len(firstResponseDurations))*0.95)]
		summary.P99FirstResponseTime = firstResponseDurations[int(float64(len(firstResponseDurations))*0.99)]
	}

	summary.TotalDuration = summary.EndTime.Sub(summary.StartTime)
	if summary.TotalDuration > 0 {
		summary.RequestsPerSecond = float64(len(s.results)) / summary.TotalDuration.Seconds()
	}

	return summary
}

// Summary contains aggregated statistics
type Summary struct {
	TotalRequests      int           `json:"total_requests"`
	SuccessfulRequests int           `json:"successful_requests"`
	FailedRequests     int           `json:"failed_requests"`
	SuccessRate        float64       `json:"success_rate"`
	StartTime          time.Time     `json:"start_time"`
	EndTime            time.Time     `json:"end_time"`
	TotalDuration      time.Duration `json:"total_duration"`
	RequestsPerSecond  float64       `json:"requests_per_second"`

	AvgCompletionTime time.Duration `json:"avg_completion_time"`
	MinCompletionTime time.Duration `json:"min_completion_time"`
	MaxCompletionTime time.Duration `json:"max_completion_time"`
	P50CompletionTime time.Duration `json:"p50_completion_time"`
	P95CompletionTime time.Duration `json:"p95_completion_time"`
	P99CompletionTime time.Duration `json:"p99_completion_time"`

	AvgFirstResponseTime time.Duration `json:"avg_first_response_time"`
	MinFirstResponseTime time.Duration `json:"min_first_response_time"`
	MaxFirstResponseTime time.Duration `json:"max_first_response_time"`
	P50FirstResponseTime time.Duration `json:"p50_first_response_time"`
	P95FirstResponseTime time.Duration `json:"p95_first_response_time"`
	P99FirstResponseTime time.Duration `json:"p99_first_response_time"`
}

// SaveResults saves all results to a JSON file
func (s *Stats) SaveResults(filename string) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	data := struct {
		Summary Summary         `json:"summary"`
		Results []RequestResult `json:"results"`
	}{
		Summary: s.GetSummary(),
		Results: s.results,
	}

	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("failed to create results file: %w", err)
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	return encoder.Encode(data)
}

// PrintSummary prints a formatted summary to stdout
func (s *Stats) PrintSummary() {
	summary := s.GetSummary()

	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("LOAD TEST SUMMARY")
	fmt.Println(strings.Repeat("=", 60))
	fmt.Printf("Total Requests:      %d\n", summary.TotalRequests)
	fmt.Printf("Successful Requests: %d\n", summary.SuccessfulRequests)
	fmt.Printf("Failed Requests:     %d\n", summary.FailedRequests)
	fmt.Printf("Success Rate:        %.2f%%\n", summary.SuccessRate)
	fmt.Printf("Total Duration:      %v\n", summary.TotalDuration)
	fmt.Printf("Requests/Second:     %.2f\n", summary.RequestsPerSecond)

	if summary.SuccessfulRequests > 0 {
		fmt.Println("\nCOMPLETION TIME STATISTICS:")
		fmt.Printf("Average:  %v\n", summary.AvgCompletionTime)
		fmt.Printf("Min:      %v\n", summary.MinCompletionTime)
		fmt.Printf("Max:      %v\n", summary.MaxCompletionTime)
		fmt.Printf("P50:      %v\n", summary.P50CompletionTime)
		fmt.Printf("P95:      %v\n", summary.P95CompletionTime)
		fmt.Printf("P99:      %v\n", summary.P99CompletionTime)

		if summary.AvgFirstResponseTime > 0 {
			fmt.Println("\nFIRST RESPONSE TIME STATISTICS:")
			fmt.Printf("Average:  %v\n", summary.AvgFirstResponseTime)
			fmt.Printf("Min:      %v\n", summary.MinFirstResponseTime)
			fmt.Printf("Max:      %v\n", summary.MaxFirstResponseTime)
			fmt.Printf("P50:      %v\n", summary.P50FirstResponseTime)
			fmt.Printf("P95:      %v\n", summary.P95FirstResponseTime)
			fmt.Printf("P99:      %v\n", summary.P99FirstResponseTime)
		}
	}
	fmt.Println(strings.Repeat("=", 60))
}

// calculateAverage calculates the average duration
func calculateAverage(durations []time.Duration) time.Duration {
	if len(durations) == 0 {
		return 0
	}
	var total time.Duration
	for _, d := range durations {
		total += d
	}
	return total / time.Duration(len(durations))
}
