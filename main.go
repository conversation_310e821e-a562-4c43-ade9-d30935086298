package main

import (
	"flag"
	"fmt"
	"log"
)

func main() {
	// Command line flags
	configFile := flag.String("config", "config.yaml", "Path to configuration file")
	flag.Parse()

	// Load configuration
	config, err := LoadConfig(*configFile)
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Create and run load tester
	loadTester := NewLoadTester(config)
	if err := loadTester.Run(); err != nil {
		log.Fatalf("Load test failed: %v", err)
	}

	fmt.Println("Load test completed successfully!")
}