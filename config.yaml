# OpenAI Image Generation Load Test Configuration

# OpenAI API Configuration
openai:
  api_key: "your-openai-api-key-here"
  base_url: "https://api.openai.com/v1"
  model: "gpt-image-1"

# Load Test Configuration
load_test:
  concurrent_requests: 50
  total_requests: 100
  timeout_seconds: 300

# Image Generation Parameters
image_params:
  size: "1024x1024"
  quality: "standard"
  n: 1

# Output Configuration
output:
  save_images: true
  images_dir: "./generated_images"
  results_file: "./load_test_results.json"
