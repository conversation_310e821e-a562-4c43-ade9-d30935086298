# OpenAI Image Generation Load Test Configuration

# OpenAI API Configuration
openai:
  api_key: "sk-s8z6jgloDmBiunXMIxHKTK2a9O2GvzTrJGFi69Zd7g4CZWHU"
  base_url: "https://okapi.manei.top"
  model: "gpt-4o-image"

# Load Test Configuration
load_test:
  concurrent_requests: 1
  total_requests: 1
  timeout_seconds: 300

# Image Generation Parameters
image_params:
  size: "1024x1024"
  quality: "standard"
  n: 1

# Output Configuration
output:
  save_images: true
  images_dir: "./generated_images"
  results_file: "./load_test_results.json"
