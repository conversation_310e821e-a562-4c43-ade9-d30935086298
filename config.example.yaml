# OpenAI Image Generation Load Test Configuration Example

# OpenAI API Configuration
openai:
  api_key: "sk-your-actual-openai-api-key-here"  # 替换为您的真实API密钥
  base_url: "https://api.openai.com/v1"
  model: "gpt-image-1"  # 使用gpt-image-1模型进行流式图像生成

# Load Test Configuration
load_test:
  concurrent_requests: 10   # 并发请求数（建议从小开始测试）
  total_requests: 20        # 总请求数
  timeout_seconds: 300      # 请求超时时间（秒）

# Image Generation Parameters
image_params:
  size: "1024x1024"    # 图像尺寸
  quality: "standard"  # 图像质量 (standard/hd)
  n: 1                 # 每次请求生成的图像数量

# Output Configuration
output:
  save_images: true                           # 是否保存生成的图像
  images_dir: "./generated_images"            # 图像保存目录
  results_file: "./load_test_results.json"   # 结果文件路径
