package main

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// LoadTester manages the load testing process
type LoadTester struct {
	config    *Config
	client    *OpenAIClient
	stats     *Stats
	prompts   []string
	generator *PromptGenerator
}

// NewLoadTester creates a new load tester
func NewLoadTester(config *Config) *LoadTester {
	timeout := time.Duration(config.LoadTest.TimeoutSeconds) * time.Second
	client := NewOpenAIClient(config.OpenAI.APIKey, config.OpenAI.BaseURL, timeout)
	stats := NewStats()
	generator := NewPromptGenerator()

	return &LoadTester{
		config:    config,
		client:    client,
		stats:     stats,
		generator: generator,
	}
}

// Run executes the load test
func (lt *LoadTester) Run() error {
	fmt.Println("Starting OpenAI Image Generation Load Test...")
	fmt.Printf("Configuration:\n")
	fmt.Printf("  Model: %s\n", lt.config.OpenAI.Model)
	fmt.Printf("  Concurrent Requests: %d\n", lt.config.LoadTest.ConcurrentRequests)
	fmt.Printf("  Total Requests: %d\n", lt.config.LoadTest.TotalRequests)
	fmt.Printf("  Image Size: %s\n", lt.config.ImageParams.Size)
	fmt.Printf("  Timeout: %ds\n", lt.config.LoadTest.TimeoutSeconds)
	fmt.Println()

	// Generate prompts
	fmt.Println("Generating prompts...")
	lt.prompts = lt.generator.GeneratePrompts(lt.config.LoadTest.TotalRequests)

	// Create output directory if needed
	if lt.config.Output.SaveImages {
		if err := os.MkdirAll(lt.config.Output.ImagesDir, 0755); err != nil {
			return fmt.Errorf("failed to create images directory: %w", err)
		}
	}

	// Create semaphore to limit concurrent requests
	semaphore := make(chan struct{}, lt.config.LoadTest.ConcurrentRequests)
	var wg sync.WaitGroup

	// Progress tracking
	completed := make(chan int, lt.config.LoadTest.TotalRequests)
	go lt.trackProgress(completed, lt.config.LoadTest.TotalRequests)

	fmt.Printf("Starting %d requests with %d concurrent workers...\n\n",
		lt.config.LoadTest.TotalRequests, lt.config.LoadTest.ConcurrentRequests)

	// Launch requests
	for i := 0; i < lt.config.LoadTest.TotalRequests; i++ {
		wg.Add(1)
		go func(requestID int, prompt string) {
			defer wg.Done()

			// Acquire semaphore
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// Execute request
			result := lt.executeRequest(requestID, prompt)
			lt.stats.AddResult(result)

			// Report progress
			completed <- requestID
		}(i+1, lt.prompts[i])
	}

	// Wait for all requests to complete
	wg.Wait()
	close(completed)

	fmt.Println("\nLoad test completed!")

	// Print summary
	lt.stats.PrintSummary()

	// Save results
	if err := lt.stats.SaveResults(lt.config.Output.ResultsFile); err != nil {
		fmt.Printf("Warning: Failed to save results: %v\n", err)
	} else {
		fmt.Printf("\nResults saved to: %s\n", lt.config.Output.ResultsFile)
	}

	return nil
}

// executeRequest executes a single image generation request
func (lt *LoadTester) executeRequest(requestID int, prompt string) RequestResult {
	result := RequestResult{
		ID:        requestID,
		Prompt:    prompt,
		StartTime: time.Now(),
	}

	// Prepare request
	req := ImageGenerationRequest{
		Model:   lt.config.OpenAI.Model,
		Prompt:  prompt,
		N:       lt.config.ImageParams.N,
		Size:    lt.config.ImageParams.Size,
		Quality: lt.config.ImageParams.Quality,
	}

	// Make API call with streaming for gpt-image-1
	streamResult, err := lt.client.GenerateImage(req)

	endTime := time.Now()
	result.EndTime = &endTime
	result.Duration = endTime.Sub(result.StartTime)

	if err != nil || streamResult.Error != nil {
		result.Success = false
		if err != nil {
			result.Error = err.Error()
		} else {
			result.Error = streamResult.Error.Error()
		}
		return result
	}

	// Check if we got a valid response
	if streamResult.Response == nil || len(streamResult.Response.Data) == 0 {
		result.Success = false
		result.Error = "no image data in response"
		return result
	}

	result.Success = true
	result.ImageURL = streamResult.Response.Data[0].URL

	// Record first response time (when we got the first streaming event)
	if streamResult.FirstResponseTime > 0 {
		firstResponseTime := result.StartTime.Add(streamResult.FirstResponseTime)
		result.FirstResponseTime = &firstResponseTime
		result.FirstResponseDuration = &streamResult.FirstResponseTime
	}

	// Download and save image if configured
	if lt.config.Output.SaveImages && result.ImageURL != "" {
		imagePath, err := lt.downloadImage(requestID, result.ImageURL)
		if err != nil {
			fmt.Printf("Warning: Failed to download image for request %d: %v\n", requestID, err)
		} else {
			result.ImagePath = imagePath
		}
	}

	return result
}

// downloadImage downloads an image from URL and saves it locally
func (lt *LoadTester) downloadImage(requestID int, imageURL string) (string, error) {
	resp, err := http.Get(imageURL)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("failed to download image: status %d", resp.StatusCode)
	}

	// Create filename
	filename := fmt.Sprintf("image_%d_%d.png", requestID, time.Now().Unix())
	filepath := filepath.Join(lt.config.Output.ImagesDir, filename)

	// Create file
	file, err := os.Create(filepath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// Copy image data
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return "", err
	}

	return filepath, nil
}

// trackProgress displays real-time progress
func (lt *LoadTester) trackProgress(completed <-chan int, total int) {
	count := 0
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case requestID, ok := <-completed:
			if !ok {
				return
			}
			count++
			if count%10 == 0 || count == total {
				fmt.Printf("Progress: %d/%d requests completed (%.1f%%)\n",
					count, total, float64(count)/float64(total)*100)
			}
			_ = requestID // Use requestID if needed for detailed logging
		case <-ticker.C:
			if count > 0 {
				summary := lt.stats.GetSummary()
				fmt.Printf("Current stats: %d completed, %.1f%% success rate, avg completion: %v\n",
					summary.TotalRequests, summary.SuccessRate, summary.AvgCompletionTime)
			}
		}
	}
}
